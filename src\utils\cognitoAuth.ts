import { fetchAuthSession, signOut } from 'aws-amplify/auth';

import { getAmplifyUser, getValidToken } from '../core/lib/auth/amplify-auth';
import { handleCognitoCallback as handleManualCallback } from '../core/lib/auth/callback-handler';
import { loginWithCognito as manualLogin } from '../core/lib/auth/cognito-manual';
import {
  cognitoConfig,
  cognitoEndpoints,
  cognitoStorageKeys,
} from './cognitoConfig';

// ==================== Token Management ====================

/**
 * Check if the access token is expired
 * This is now handled automatically by Amplify.
 */
export async function isTokenExpired(): Promise<boolean> {
  const token = await getValidToken();
  return !token;
}

/**
 * Checks if the token is expired and refreshes it if necessary.
 * Amplify handles this automatically via getValidToken.
 */
export async function checkAndRefreshCognitoToken(): Promise<boolean> {
  const token = await getValidToken();
  return !!token;
}

/**
 * Get the stored access token
 */
export async function getCognitoAccessToken(): Promise<string | null> {
  return getValidToken();
}

/**
 * Get the stored ID token
 */
export async function getCognitoIdToken(): Promise<string | null> {
  return getValidToken();
}

/**
 * Clear all Cognito tokens and user info
 */
export async function clearCognitoTokens(): Promise<void> {
  try {
    // Amplify signOut clears the storage (sessionStorage in our case)
    await signOut({ global: false });

    // Clear any additional custom keys from sessionStorage
    sessionStorage.removeItem(cognitoStorageKeys.userInfo);
    sessionStorage.removeItem(cognitoStorageKeys.authProvider);
    sessionStorage.removeItem('cognito_processing_callback');

    // CRITICAL: Ensure no residue in localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('emrUserInfo');
    localStorage.removeItem('userRole');
    localStorage.removeItem('user');

    Object.keys(localStorage)
      .filter((key) => key.includes('Cognito') || key.includes('amplify'))
      .forEach((key) => localStorage.removeItem(key));
  } catch (error) {
    console.error('Error clearing tokens:', error);
  }
}

// ==================== Authentication Flow ====================

/**
 * Initiate Cognito login with Manual PKCE flow to force tab isolation
 */
export async function cognitoLogin(): Promise<void> {
  await manualLogin();
}

/**
 * Handle the OAuth callback after Cognito redirect
 */
export async function handleCognitoCallback(): Promise<boolean> {
  try {
    const searchParams = new URLSearchParams(window.location.search);
    if (!searchParams.has('code')) return false;

    await handleManualCallback(searchParams);
    sessionStorage.setItem(cognitoStorageKeys.authProvider, 'cognito');
    return true;
  } catch (error) {
    console.error('handleCognitoCallback error:', error);
    return false;
  }
}

/**
 * Get stored Cognito user info
 */
export async function getCognitoUserInfo(): Promise<any | null> {
  try {
    const user = await getAmplifyUser();
    const session = await fetchAuthSession();
    const idToken = session?.tokens?.idToken;

    if (!idToken) {
      console.warn('getCognitoUserInfo: No idToken available');
      return null;
    }

    const payload = idToken.payload as any;

    // Multiple fallback strategy for email
    let email =
      payload.email ||
      payload.preferred_username ||
      (user as any)?.signInDetails?.loginId ||
      '';

    // If username looks like an email, use it as fallback
    const username =
      user?.username || payload['cognito:username'] || payload.sub;
    if (!email && username && username.includes('@')) {
      email = username;
    }

    return {
      username,
      userId: user?.userId || payload.sub,
      email,
    };
  } catch (error) {
    console.error('Failed to get user info:', error);
    return null;
  }
}

/**
 * Logout from Cognito
 */
export async function cognitoLogout(): Promise<void> {
  try {
    // 1. Clear local tokens and Amplify session
    await clearCognitoTokens();

    // 2. Clear pre-login URL
    sessionStorage.removeItem('preLoginUrl');

    // 3. Construct the server-side logout URL
    const params = new URLSearchParams({
      client_id: cognitoConfig.clientId,
      logout_uri: cognitoConfig.postLogoutRedirectUri,
    });

    const logoutUrl = `${cognitoEndpoints.logout}?${params.toString()}`;
    window.location.href = logoutUrl;
  } catch (error) {
    console.error('Cognito logout failed:', error);
    window.location.href = '/login';
  }
}

/**
 * Check if user is authenticated with Cognito
 */
export async function isCognitoAuthenticated(): Promise<boolean> {
  const token = await getValidToken();
  return !!token;
}

/**
 * Refresh the access token
 * Amplify handles this automatically.
 */
export async function refreshCognitoToken(): Promise<boolean> {
  const token = await getValidToken();
  return !!token;
}
