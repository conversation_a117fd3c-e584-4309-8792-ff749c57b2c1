import {
  createAsyncThunk,
  createSlice,
  isRejected,
  PayloadAction,
  SerializedError,
} from '@reduxjs/toolkit';

import { isOrgAdminRole, isSuperAdminRole } from '../../../constants/roles';
import { AuthState, Organization, Role, User } from '../../../types';
import { cognitoLogout } from '../../../utils/cognitoAuth';
import { fetchOrganizationById } from '../organizations/organization.slice';
import authService, { EmrUserInfo } from './auth.service';

// Import RootState type for proper typing
interface RootState {
  organizations: {
    allOrganizations: Organization[];
  };
}

interface ApiErrorResponse {
  data?: {
    message?: string;
  };
}

interface ApiError extends SerializedError {
  response?: ApiErrorResponse;
}

// Try to get selected organization from sessionStorage if it exists (for tab isolation)
const getInitialSelectedOrganization = () => {
  try {
    const org = sessionStorage.getItem('selectedOrganization');
    const userRole = sessionStorage.getItem('userRole');

    // Only restore selected organization for super admins
    const isLikelySuperAdmin = isSuperAdminRole(userRole);

    return org && isLikelySuperAdmin ? JSON.parse(org) : null;
  } catch (error) {
    console.error(
      'Failed to parse selected organization from sessionStorage',
      error
    );
    return null;
  }
};

// Helper function to save organization to sessionStorage
const saveOrganizationToSessionStorage = (org: Organization | null) => {
  try {
    if (org) {
      sessionStorage.setItem('selectedOrganization', JSON.stringify(org));
    } else {
      sessionStorage.removeItem('selectedOrganization');
    }
  } catch (error) {
    console.error('Failed to save organization to sessionStorage', error);
  }
};

const initialState: AuthState = {
  user: null,
  selectedOrganization: getInitialSelectedOrganization(),
  currentOrganization: null,
  loading:
    typeof window !== 'undefined' &&
    (window.location.search.includes('code=') ||
      !!sessionStorage.getItem('cognito_processing_callback')),
  error: null,
  successMessage: null,
  isAuthenticated: false,
  isSuperAdmin: false,
  isOrganizationAdmin: false,
  isOrganizationUser: false,
  loggingOut: false,
  emrUserInfo: null,
};

// Create a thunk that doesn't trigger loading state for organization selection
export const selectOrganization = createAsyncThunk(
  'auth/selectOrganization',
  async (organizationId: string, { rejectWithValue, getState, dispatch }) => {
    try {
      // Get organization from the organizations state
      const state = getState() as RootState;
      const allOrganizations = state.organizations.allOrganizations;

      const organization = allOrganizations.find(
        (org: Organization) => org.id === organizationId
      );

      if (!organization) {
        // If not found in the store, try to fetch it
        try {
          const result = await dispatch(fetchOrganizationById(organizationId));
          if (fetchOrganizationById.fulfilled.match(result)) {
            const fetchedOrg = result.payload;
            // Save the fetched organization to sessionStorage
            sessionStorage.setItem(
              'selectedOrganization',
              JSON.stringify(fetchedOrg)
            );
            return fetchedOrg;
          }
          throw new Error('Failed to fetch organization');
        } catch (fetchError) {
          console.error('Error fetching organization:', fetchError);
        }
      }

      // Store selected organization in sessionStorage
      sessionStorage.setItem(
        'selectedOrganization',
        JSON.stringify(organization)
      );

      // Also update the current organization
      dispatch(setSelectedOrganization(organization as Organization));

      return organization;
    } catch (error) {
      console.error('Error in selectOrganization:', error);
      return rejectWithValue(error);
    }
  }
);

export const fetchEmrUserInfo = createAsyncThunk(
  'auth/fetchEmrUserInfo',
  async (email: string, { rejectWithValue }) => {
    try {
      const { user, token } = await authService.fetchEmrUserInfo(email);
      return { user, token };
    } catch (error) {
      const typedError = error as ApiError;
      return rejectWithValue(
        typedError.response?.data?.message ||
          'Failed to fetch user info from EMR'
      );
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { dispatch }) => {
    try {
      // Set loggingOut flag to true
      dispatch(setLoggingOut(true));

      const { resetRedirectState } = await import(
        '../../../utils/redirect-utils'
      );

      // Clear all storage
      sessionStorage.clear();
      localStorage.clear();

      // Clear cookies
      document.cookie.split(';').forEach((cookie) => {
        const [name] = cookie.trim().split('=');
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      });

      // Reset the redirect state
      resetRedirectState();

      try {
        // Perform Cognito logout
        await cognitoLogout();
        // Reset the auth state
        dispatch({ type: 'RESET_ALL_SLICES' });
      } catch (logoutError) {
        console.error('Cognito logout error:', logoutError);
        window.location.href = '/login';
      }

      return true;
    } catch (error) {
      console.error('Error during logout:', error);
      sessionStorage.clear();
      localStorage.clear();
      window.location.href = '/login';
      return false;
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoggingOut(state, action: PayloadAction<boolean>) {
      state.loggingOut = action.payload;
    },
    setAuthState: (
      state,
      action: PayloadAction<{
        user: User;
        selectedOrganization: Organization | null;
        currentOrganization?: Organization | null;
      }>
    ) => {
      state.user = action.payload.user;
      state.isAuthenticated = true;

      // Set currentOrganization if provided
      if (action.payload.currentOrganization) {
        state.currentOrganization = action.payload.currentOrganization;
      }

      // If no organization is provided but user is organization admin, use their organization
      if (
        !action.payload.selectedOrganization &&
        action.payload.user.organization
      ) {
        const isOrgAdmin = action.payload.user.roles?.some((role: Role) =>
          isOrgAdminRole(role.name)
        );
        const isSuperAdminRoleResult = action.payload.user.roles?.some(
          (role: Role) => isSuperAdminRole(role.name)
        );
        if (isOrgAdmin && !isSuperAdminRoleResult) {
          state.selectedOrganization = action.payload.user.organization;
        } else {
          state.selectedOrganization = action.payload.selectedOrganization;
        }
      } else {
        state.selectedOrganization = action.payload.selectedOrganization;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    setSelectedOrganization: (
      state,
      action: PayloadAction<Organization | null>
    ) => {
      state.selectedOrganization = action.payload;
      saveOrganizationToSessionStorage(action.payload);
    },
    clearSelectedOrganization: (state) => {
      state.selectedOrganization = null;
      sessionStorage.removeItem('selectedOrganization');
    },
    setEmrUserInfo(state, action: PayloadAction<EmrUserInfo | null>) {
      state.emrUserInfo = action.payload;

      // Update user's organizationName from emrUserInfo for non-super admin users
      if (
        state.user &&
        action.payload &&
        !state.user.roles?.some((role: Role) => isSuperAdminRole(role.name))
      ) {
        if (action.payload.organizationName) {
          state.user.organizationName = action.payload.organizationName;
          // Also update in sessionStorage
          try {
            const storedUser = sessionStorage.getItem('user');
            if (storedUser) {
              const userObj = JSON.parse(storedUser);
              userObj.organizationName = action.payload.organizationName;
              sessionStorage.setItem('user', JSON.stringify(userObj));
            }
          } catch (e) {
            console.warn(
              'Failed to update user organizationName in sessionStorage',
              e
            );
          }
        }
      }
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Handle fetchEmrUserInfo
    builder
      .addCase(fetchEmrUserInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchEmrUserInfo.fulfilled,
        (
          state,
          action: PayloadAction<{ user: EmrUserInfo; token: string }>
        ) => {
          state.loading = false;
          state.emrUserInfo = action.payload.user;

          // Update user's organizationName from emrUserInfo for non-super admin users
          if (
            state.user &&
            !state.user.roles?.some((role: Role) => isSuperAdminRole(role.name))
          ) {
            if (action.payload.user.organizationName) {
              state.user.organizationName =
                action.payload.user.organizationName;
              // Also update in sessionStorage
              try {
                const storedUser = sessionStorage.getItem('user');
                if (storedUser) {
                  const userObj = JSON.parse(storedUser);
                  userObj.organizationName =
                    action.payload.user.organizationName;
                  sessionStorage.setItem('user', JSON.stringify(userObj));
                }
              } catch (e) {
                console.warn(
                  'Failed to update user organizationName in sessionStorage',
                  e
                );
              }
            }
          }

          // Store the token in sessionStorage
          if (action.payload.token) {
            sessionStorage.setItem('token', action.payload.token);
          }
        }
      )
      .addCase(fetchEmrUserInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Other reducers
    builder
      .addCase(selectOrganization.fulfilled, (state, action) => {
        state.selectedOrganization = action.payload;
        state.error = null;
        sessionStorage.setItem(
          'selectedOrganization',
          JSON.stringify(action.payload)
        );
        if (!state.currentOrganization) {
          state.currentOrganization = action.payload;
        }
      })
      .addCase(fetchOrganizationById.fulfilled, (state, action) => {
        const isSuperAdmin = state.user?.roles?.some((role: Role) =>
          isSuperAdminRole(role.name)
        );
        if (isSuperAdmin) return;

        state.currentOrganization = action.payload;
        sessionStorage.setItem(
          'currentOrganization',
          JSON.stringify(action.payload)
        );

        if (state.user) {
          const isOrgAdmin = state.user.roles?.some((role: Role) =>
            isOrgAdminRole(role.name)
          );
          if (isOrgAdmin) {
            state.selectedOrganization = action.payload;
            sessionStorage.setItem(
              'selectedOrganization',
              JSON.stringify(action.payload)
            );
          }
        }
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.currentOrganization = null;
        state.selectedOrganization = null;
        state.loggingOut = false;
      })
      .addCase(logoutUser.pending, (state) => {
        state.loggingOut = true;
      })
      .addMatcher(isRejected(selectOrganization), (state, action) => {
        const error = action.payload as ApiError;
        state.error =
          error?.response?.data?.message ||
          error?.message ||
          'An error occurred';
      });
  },
});

export const {
  setAuthState,
  clearError,
  clearMessages,
  clearSuccessMessage,
  setSelectedOrganization,
  clearSelectedOrganization,
  setEmrUserInfo,
  setLoggingOut,
  setLoading,
} = authSlice.actions;
export default authSlice.reducer;
