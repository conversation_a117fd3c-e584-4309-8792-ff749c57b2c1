import { Edit, Pencil, Plus, Trash2 } from 'lucide-react';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch } from 'react-redux';

import AppButton from '../../components/Common/AppButton';
import ConfirmationModal from '../../components/Common/ConfirmationModal';
import DataTable from '../../components/Common/DataTable';
import Modal from '../../components/Common/Modal';
import MUISelect from '../../components/Common/MUISelect';
import SearchBar from '../../components/Common/SearchBar';
import { useToast } from '../../contexts/ToastContext';
import { AppDispatch } from '../../store';
import { fetchAllOrganizations } from '../../store/features/organizations/organization.slice';
import {
  getSubscriberById,
  getSubscribers,
  type GetSubscribersParams,
  mapUserToClinicOrg,
  SubscriberDetail,
  unmapUserFromClinicOrg,
} from '../../store/features/subscription/subscriber.service';
import {
  deleteSubscriptionPlan,
  getAllSubscriptionPlans,
  getOrganizationPlan,
  SubscriptionPlan,
} from '../../store/features/subscription/subscription.service';
import SubscriberForm from '../dashboard/components/SubscriberForm';
import AddToOrganizationModal from './components/AddToOrganizationModal';
import EnterprisePlanModal from './components/EnterprisePlanModal';
import PlanEditModal from './components/PlanEditModal';
import PlanFormModal from './components/PlanFormModal';
import RemoveFromOrganizationModal from './components/RemoveFromOrganizationModal';

// Helper function to check if error is from a cancelled request
const isCancelledRequestError = (error: unknown): boolean => {
  if (!error) return false;
  const errorObj = error as Record<string, unknown>;
  return (
    errorObj?.name === 'RequestCancelledError' ||
    errorObj?.name === 'CanceledError' ||
    errorObj?.name === 'Cancel' ||
    errorObj?.code === 'ERR_CANCELED' ||
    errorObj?.code === 'CANCELLED' ||
    (typeof errorObj?.message === 'string' &&
      errorObj.message.toLowerCase().includes('cancel'))
  );
};

export const calculateTotalWithTax = (totalAmount: number): number => {
  if (!totalAmount || totalAmount <= 0) return 0;

  const taxRate = 0.18;
  const totalledAmount = totalAmount + totalAmount * taxRate;

  return totalledAmount;
};

// Helper to extract a readable message from API errors
const getErrorMessage = (error: unknown, fallbackMessage: string): string => {
  if (!error) return fallbackMessage;

  const errorWithResponse = error as {
    response?: { data?: unknown };
    message?: string;
  };

  const responseData = errorWithResponse.response?.data;

  if (typeof responseData === 'string' && responseData.trim().length > 0) {
    return responseData;
  }

  if (
    responseData &&
    typeof responseData === 'object' &&
    !Array.isArray(responseData)
  ) {
    const data = responseData as Record<string, unknown>;

    const candidateMessage = [
      data.message,
      data.error,
      data.errorMessage,
      data.detail,
      data.title,
    ].find((value) => typeof value === 'string' && value.trim().length > 0);
    if (typeof candidateMessage === 'string') {
      return candidateMessage;
    }

    if (Array.isArray(data.errors)) {
      const firstError = data.errors.find((item) => {
        if (typeof item === 'string') {
          return item.trim().length > 0;
        }
        if (item && typeof item === 'object') {
          const message = (item as { message?: string }).message;
          return typeof message === 'string' && message.trim().length > 0;
        }
        return false;
      });
      if (typeof firstError === 'string') {
        return firstError;
      }
      if (firstError && typeof firstError === 'object') {
        const message = (firstError as { message?: string }).message;
        if (typeof message === 'string' && message.trim().length > 0) {
          return message;
        }
      }
    }

    if (
      data.errors &&
      typeof data.errors === 'object' &&
      data.errors !== null
    ) {
      const collectedErrors: string[] = [];
      Object.values(data.errors).forEach((value) => {
        if (typeof value === 'string' && value.trim().length > 0) {
          collectedErrors.push(value);
        } else if (Array.isArray(value)) {
          value.forEach((item) => {
            if (typeof item === 'string' && item.trim().length > 0) {
              collectedErrors.push(item);
            }
          });
        }
      });

      if (collectedErrors.length > 0) {
        const [firstCollected] = collectedErrors;
        if (typeof firstCollected === 'string') {
          return firstCollected;
        }
      }
    }
  }

  if (
    errorWithResponse.message &&
    typeof errorWithResponse.message === 'string'
  ) {
    return errorWithResponse.message;
  }

  return fallbackMessage;
};

const DEFAULT_SUBSCRIBER_PAGE_LIMIT = 20;

interface Subscriber {
  id: string;
  no: number;
  organisationName: string;
  adminEmail: string;
  planName: string;
  planType: string;
  amount: string;
  validityStartDate: string;
  validityExpiryDate: string;
  status: 'Active' | 'Expired' | 'Cancelled' | 'Pending' | 'Free Trial';
  subscriptionType?: string;
  organizationType?: string;
  billingValidity?: string;
}

interface RequestedSubscriber {
  id: string;
  name: string;
  email: string;
  currentOrganization: string;
  requestedClinicName: string;
  planName: string;
  planStatus: string;
}

const PaymentList: React.FC = () => {
  const [activeTab, setActiveTab] = useState<
    'subscriptions' | 'subscribers' | 'requested-subscribers'
  >('subscriptions');
  const [searchValue, setSearchValue] = useState('');
  const [requestedSearchValue, setRequestedSearchValue] = useState('');
  const [selectedSubscription, setSelectedSubscription] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [requestedStatus, setRequestedStatus] = useState<
    'pending_setup' | 'setup_complete'
  >('pending_setup');
  const [isSubscriberModalOpen, setIsSubscriberModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingSubscriberId, setEditingSubscriberId] = useState<string | null>(
    null
  );
  const [editingSubscriberData, setEditingSubscriberData] =
    useState<SubscriberDetail | null>(null);
  const [isLoadingSubscriber, setIsLoadingSubscriber] = useState(false);
  const [planFilterOptions, setPlanFilterOptions] = useState<
    Array<{ label: string; value: string }>
  >([{ label: 'All Plans', value: '' }]);
  const [arePlanFiltersLoaded, setArePlanFiltersLoaded] = useState(false);

  const [isPlanModalOpen, setIsPlanModalOpen] = useState(false);
  const [isEditPlanOpen, setIsEditPlanOpen] = useState(false);
  const [isEnterpriseModalOpen, setIsEnterpriseModalOpen] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [planToDelete, setPlanToDelete] = useState<SubscriptionPlan | null>(
    null
  );
  const [deletingPlanId, setDeletingPlanId] = useState<string | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loadingPlans, setLoadingPlans] = useState(false);
  const [organizationPlan, setOrganizationPlan] =
    useState<SubscriptionPlan | null>(null);
  const [loadingOrganizationPlan, setLoadingOrganizationPlan] = useState(false);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [requestedSubscribers, setRequestedSubscribers] = useState<
    RequestedSubscriber[]
  >([]);
  const [loadingSubscribers, setLoadingSubscribers] = useState(false);
  const [loadingRequestedSubscribers, setLoadingRequestedSubscribers] =
    useState(false);
  const [page, setPage] = useState(1);
  const [requestedPage, setRequestedPage] = useState(1);
  const [limit, setLimit] = useState(DEFAULT_SUBSCRIBER_PAGE_LIMIT);
  const [totalSubscribers, setTotalSubscribers] = useState(0);
  const [totalRequestedSubscribers, setTotalRequestedSubscribers] = useState(0);

  // State for requested subscribers functionality
  const [selectedRequestedSubscribers, setSelectedRequestedSubscribers] =
    useState<string[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [isMappingLoading, setIsMappingLoading] = useState(false);

  const { error: showError, success } = useToast();
  const dispatch = useDispatch<AppDispatch>();
  const hasFetchedPlansRef = useRef(false);
  const hasFetchedSubscribersRef = useRef(false);
  const subscribersQueryKeyRef = useRef<string | null>(null);
  const requestedSubscribersQueryKeyRef = useRef<string | null>(null);

  const sanitizeHtml = useCallback((html?: string): string => {
    if (!html) return '';
    if (typeof window === 'undefined') {
      return html;
    }
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    doc
      .querySelectorAll('script, style, iframe, object')
      .forEach((el) => el.remove());
    return doc.body.innerHTML || '';
  }, []);

  const richTextStyles = useMemo(
    () => `
      .rich-text-wrapper {
        position: relative;
        max-height: 120px;
        overflow: hidden;
      }
      .rich-text-wrapper.org-plan {
        padding-left: 0.5rem;
      }
      .rich-text-wrapper.org-plan::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 36px;
        pointer-events: none;
        background: linear-gradient(
          180deg,
          rgba(229, 231, 235, 0) 0%,
          rgba(229, 231, 235, 1) 90%
        );
      }
      .rich-text-content {
        font-size: 0.875rem;
        line-height: 1.5;
        color: rgb(75, 85, 99);
      }
      .rich-text-content h1,
      .rich-text-content h2,
      .rich-text-content h3,
      .rich-text-content h4,
      .rich-text-content h5,
      .rich-text-content h6 {
        margin: 0 0 0.5rem 0;
        font-weight: 600;
      }
      .rich-text-content ul,
      .rich-text-content ol {
        padding-left: 1.25rem;
        margin: 0.5rem 0;
        list-style-position: outside;
      }
      .rich-text-content ul {
        list-style-type: disc;
      }
      .rich-text-content ol {
        list-style-type: decimal;
      }
      .rich-text-content li {
        margin-bottom: 0.25rem;
        color: inherit;
      }
      .rich-text-content li::marker {
        color: inherit;
        font-size: inherit;
        font-weight: inherit;
      }
      .rich-text-content p {
        margin: 0 0 0.5rem 0;
      }
    `,
    []
  );

  const statusOptions = useMemo(
    () => [
      { label: 'All Status', value: '' },
      { label: 'Active', value: 'Active' },
      { label: 'Expired', value: 'Expired' },
      { label: 'Cancelled', value: 'Cancelled' },
      { label: 'Pending', value: 'Pending' },
      { label: 'Free Trial', value: 'Free Trial' },
    ],
    []
  );

  const requestedStatusOptions = useMemo(
    () => [
      { label: 'Pending Requests', value: 'pending_setup' },
      { label: 'Completed Requests', value: 'setup_complete' },
    ],
    []
  );
  const columns = [
    {
      key: 'no' as const,
      label: 'No.',
      render: (_: unknown, subscriber: Subscriber) => (
        <span className='font-medium'>
          {String(subscriber.no).padStart(2, '0')}
        </span>
      ),
    },
    {
      key: 'organisationName' as const,
      label: 'Organisation Name',
      render: (_: unknown, subscriber: Subscriber) =>
        subscriber.organisationName,
    },
    {
      key: 'adminEmail' as const,
      label: 'Admin Email',
      render: (_: unknown, subscriber: Subscriber) => subscriber.adminEmail,
    },
    {
      key: 'planName' as const,
      label: 'Plan Name',
      render: (_: unknown, subscriber: Subscriber) => subscriber.planName,
    },
    {
      key: 'organizationType' as const,
      label: 'Organization/ Clinic',
      render: (_: unknown, subscriber: Subscriber) =>
        subscriber.organizationType || '--',
    },
    {
      key: 'planType' as const,
      label: 'Plan Type',
      render: (_: unknown, subscriber: Subscriber) => {
        if (subscriber.status === 'Free Trial') {
          return '7 days';
        }
        return subscriber.billingValidity || subscriber.planType || '--';
      },
    },
    {
      key: 'amount' as const,
      label: 'Amount (₹)',
      render: (_: unknown, subscriber: Subscriber) => subscriber.amount,
    },
    {
      key: 'validityStartDate' as const,
      label: 'Validity Start Date',
      render: (_: unknown, subscriber: Subscriber) =>
        subscriber.validityStartDate,
    },
    {
      key: 'validityExpiryDate' as const,
      label: 'Validity End Date',
      render: (_: unknown, subscriber: Subscriber) =>
        subscriber.validityExpiryDate,
    },
    {
      key: 'status' as const,
      label: 'Status',
      render: (_: unknown, subscriber: Subscriber) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap inline-block ${getStatusBadgeClasses(
            subscriber.status
          )}`}
        >
          {subscriber.status}
        </span>
      ),
    },
  ];

  const requestedColumns = [
    {
      key: 'name' as const,
      label: 'Name',
      render: (_: unknown, subscriber: RequestedSubscriber) => subscriber.name,
    },
    {
      key: 'email' as const,
      label: 'Email',
      render: (_: unknown, subscriber: RequestedSubscriber) => subscriber.email,
    },
    {
      key: 'currentOrganization' as const,
      label: 'Current Organization',
      render: (_: unknown, subscriber: RequestedSubscriber) =>
        subscriber.currentOrganization,
    },
    {
      key: 'requestedClinicName' as const,
      label: 'Requested Clinic Name',
      render: (_: unknown, subscriber: RequestedSubscriber) =>
        subscriber.requestedClinicName,
    },
    {
      key: 'planName' as const,
      label: 'Plan Name',
      render: (_: unknown, subscriber: RequestedSubscriber) =>
        subscriber.planName,
    },
    {
      key: 'planStatus' as const,
      label: 'Plan Status',
      render: (_: unknown, subscriber: RequestedSubscriber) => {
        const statusLower = subscriber.planStatus.toLowerCase();
        let className = 'bg-blue-100 text-blue-800';
        if (statusLower === 'expired') {
          className = 'bg-orange-100 text-orange-800';
        } else if (statusLower === 'free trial') {
          className = 'bg-purple-100 text-purple-800';
        } else if (statusLower === 'active') {
          className = 'bg-green-100 text-green-800';
        }
        return (
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap inline-block ${className}`}
          >
            {subscriber.planStatus}
          </span>
        );
      },
    },
  ];

  const handleCloseModal = () => {
    setIsSubscriberModalOpen(false);
    setIsEditMode(false);
    setEditingSubscriberId(null);
    setEditingSubscriberData(null);
    setIsLoadingSubscriber(false);
  };

  const renderRichText = useCallback(
    (html?: string, fallback?: string, useFade = false) => {
      const sanitized = sanitizeHtml(html);
      if (!sanitized) {
        return (
          <p className='text-sm text-gray-600'>{fallback ?? 'Description'}</p>
        );
      }
      return (
        <div className={`rich-text-wrapper${useFade ? ' org-plan' : ''}`}>
          <div
            className='rich-text-content'
            dangerouslySetInnerHTML={{ __html: sanitized }}
          />
        </div>
      );
    },
    [sanitizeHtml]
  );

  const updatePlanFilterOptions = useCallback(
    (plansList?: SubscriptionPlan[]) => {
      if (!plansList || !Array.isArray(plansList)) return;

      const uniquePlans = plansList
        .filter((plan) => plan.planName && plan.id)
        .map((plan) => ({
          label: plan.planName ?? plan.id ?? '',
          value: plan.id ?? plan.planName ?? '',
        }))
        .filter((option) => option.value);

      const uniqueByValue = new Map<string, { label: string; value: string }>();
      uniquePlans.forEach((option) => {
        if (!uniqueByValue.has(option.value)) {
          uniqueByValue.set(option.value, option);
        }
      });

      const planOptions = Array.from(uniqueByValue.values());
      setPlanFilterOptions([{ label: 'All Plans', value: '' }, ...planOptions]);

      setArePlanFiltersLoaded(true);

      if (
        selectedSubscription &&
        !plansList.some((plan) => plan.id === selectedSubscription)
      ) {
        setSelectedSubscription('');
      }
    },
    [selectedSubscription]
  );

  const fetchOrganizationPlan = useCallback(async () => {
    setLoadingOrganizationPlan(true);
    try {
      const plan = await getOrganizationPlan();
      setOrganizationPlan(plan);
    } catch (err: unknown) {
      if (isCancelledRequestError(err)) {
        setLoadingOrganizationPlan(false);
        return;
      }
      const errorMessage =
        (err as { response?: { data?: { message?: string } } })?.response?.data
          ?.message ||
        (err as { message?: string })?.message ||
        'Failed to load organisation plan';
      showError('Error', errorMessage);
    } finally {
      setLoadingOrganizationPlan(false);
    }
  }, [showError]);

  const loadPlans = useCallback(
    async (options?: { force?: boolean }) => {
      if (!options?.force && hasFetchedPlansRef.current) {
        return true;
      }

      setLoadingPlans(true);
      try {
        const response = await getAllSubscriptionPlans();
        if (response.plans && Array.isArray(response.plans)) {
          setPlans(response.plans);
          updatePlanFilterOptions(response.plans);
          hasFetchedPlansRef.current = true;
          return true;
        }
        showError('Error', 'Failed to fetch plans');
        return false;
      } catch (err: unknown) {
        if (isCancelledRequestError(err)) {
          return false;
        }
        const errorMessage =
          (err as { response?: { data?: { message?: string } } })?.response
            ?.data?.message ||
          (err as { message?: string })?.message ||
          'An error occurred while fetching plans';
        showError('Error', errorMessage);
        return false;
      } finally {
        setLoadingPlans(false);
      }
    },
    [showError, updatePlanFilterOptions]
  );

  const refreshPlansWithDelay = useCallback(
    async (delay = 0) => {
      if (delay > 0) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
      const success = await loadPlans({ force: true });
      if (!success) {
        setTimeout(() => {
          loadPlans({ force: true });
        }, 1000);
      }
    },
    [loadPlans]
  );

  const handleAddUser = useCallback(() => {
    loadPlans();
    setIsEditMode(false);
    setEditingSubscriberId(null);
    setEditingSubscriberData(null);
    setIsSubscriberModalOpen(true);
  }, [loadPlans]);

  const handleEdit = useCallback(
    (subscriber: Subscriber) => {
      loadPlans();
      setIsEditMode(true);
      setEditingSubscriberId(subscriber.id);
      setEditingSubscriberData(null);
      setIsSubscriberModalOpen(true);
    },
    [loadPlans]
  );

  const subscriberActions = useCallback(
    (subscriber: Subscriber) => {
      console.log('Subscriber Action Object:', subscriber);
      return [
        {
          label: 'Edit',
          icon: <Edit className='w-4 h-4' />,
          onClick: () => handleEdit(subscriber),
          disabled:
            subscriber.status === 'Expired' ||
            (subscriber.planType?.toLowerCase() === 'clinic' &&
              subscriber.subscriptionType?.toLowerCase() !== 'organization'),
        },
      ];
    },
    [handleEdit]
  );

  // Fetch subscription plans
  useEffect(() => {
    if (activeTab !== 'subscriptions') return;
    loadPlans();
  }, [activeTab, loadPlans]);

  useEffect(() => {
    if (arePlanFiltersLoaded || activeTab !== 'subscribers') return;

    if (plans.length) {
      updatePlanFilterOptions(plans);
      return;
    }

    loadPlans({ force: true });
  }, [
    activeTab,
    arePlanFiltersLoaded,
    loadPlans,
    plans,
    updatePlanFilterOptions,
  ]);

  useEffect(() => {
    fetchOrganizationPlan();
  }, [fetchOrganizationPlan]);

  const handlePlanCreated = () => {
    refreshPlansWithDelay(500);
  };

  const handlePlanModalClose = () => {
    setIsPlanModalOpen(false);
  };

  const handlePlanUpdated = () => {
    setIsEditPlanOpen(false);
    setSelectedPlanId(null);
    refreshPlansWithDelay(500);
  };

  const getAllFeatureNames = (plan: SubscriptionPlan): string[] => {
    const features: string[] = [];

    const normalize = (value?: string) => value?.trim() || undefined;
    const resolveFeatureName = (feature: {
      featureName?: string;
      featureId?: string;
    }) =>
      normalize(feature.featureName) || feature.featureId || 'Unknown Feature';

    if (plan.features.MRD) {
      features.push(...plan.features.MRD.map(resolveFeatureName));
    }
    if (plan.features.EMR) {
      features.push(...plan.features.EMR.map(resolveFeatureName));
    }
    if (plan.features.Billing) {
      features.push(...plan.features.Billing.map(resolveFeatureName));
    }

    if (plan.addOnFeatures.MRD) {
      features.push(...plan.addOnFeatures.MRD.map(resolveFeatureName));
    }
    if (plan.addOnFeatures.EMR) {
      features.push(...plan.addOnFeatures.EMR.map(resolveFeatureName));
    }
    if (plan.addOnFeatures.Billing) {
      features.push(...plan.addOnFeatures.Billing.map(resolveFeatureName));
    }

    return features;
  };

  // Helper function to format validity text
  const formatValidity = (validity: string): string => {
    if (validity === 'Both') {
      return 'Monthly & Yearly Subscription';
    }
    if (validity === 'Monthly') {
      return 'Monthly Subscription';
    }
    if (validity === 'Yearly') {
      return 'Yearly Subscription';
    }
    return 'Monthly & Yearly Subscription';
  };

  const formatValidityWithAmounts = (plan: SubscriptionPlan): string => {
    const monthlyBasic = plan.totalMonthlyBasicAmount ?? null;
    const yearlyBasic = plan.totalYearlyBasicAmount ?? null;
    const showMonthly = plan.validity === 'Monthly' || plan.validity === 'Both';
    const showYearly = plan.validity === 'Yearly' || plan.validity === 'Both';

    if (plan.validity === 'Monthly' && showMonthly && monthlyBasic !== null) {
      return `Monthly (₹${monthlyBasic.toLocaleString('en-IN')}) Subscription`;
    }

    if (plan.validity === 'Yearly' && showYearly && yearlyBasic !== null) {
      return `Yearly (₹${yearlyBasic.toLocaleString('en-IN')}) Subscription`;
    }

    if (
      showMonthly &&
      showYearly &&
      monthlyBasic !== null &&
      yearlyBasic !== null
    ) {
      return `Monthly (₹${monthlyBasic.toLocaleString(
        'en-IN'
      )}) & Yearly (₹${yearlyBasic.toLocaleString('en-IN')}) Subscription`;
    }

    return formatValidity(plan.validity);
  };

  // Handle edit plan
  const handleEditPlan = (planId: string) => {
    setSelectedPlanId(planId);
    setIsEditPlanOpen(true);
  };

  const handleDeleteClick = (plan: SubscriptionPlan) => {
    setPlanToDelete(plan);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!planToDelete?.id) return;

    setDeletingPlanId(planToDelete.id);
    try {
      const response = await deleteSubscriptionPlan(planToDelete.id);

      success('Success', response?.message || 'Plan deleted successfully');
      setIsDeleteModalOpen(false);
      setPlanToDelete(null);
      setDeletingPlanId(null);

      refreshPlansWithDelay(500);
    } catch (err: unknown) {
      if (isCancelledRequestError(err)) {
        setDeletingPlanId(null);
        return;
      }
      const errorMessage = getErrorMessage(
        err,
        'An error occurred while deleting the plan'
      );
      showError('Error', errorMessage);
      setDeletingPlanId(null);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
    setPlanToDelete(null);
  };

  const formatCurrency = (value: unknown): string => {
    if (typeof value === 'number' && !Number.isNaN(value)) {
      return value.toLocaleString('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    }
    if (typeof value === 'string' && value.trim() !== '') {
      const numericValue = Number(value);
      if (!Number.isNaN(numericValue)) {
        return numericValue.toLocaleString('en-IN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      }
      return value;
    }
    return '0.00';
  };

  const formatDateString = (value: unknown): string => {
    if (!value) {
      return '--';
    }
    if (typeof value === 'string') {
      const parsedDate = new Date(value);
      if (!Number.isNaN(parsedDate.getTime())) {
        return parsedDate.toLocaleDateString('en-GB');
      }
      return value;
    }
    if (value instanceof Date && !Number.isNaN(value.getTime())) {
      return value.toLocaleDateString('en-GB');
    }
    return String(value);
  };

  const formatSubscriberStatus = (
    value: unknown,
    fallBackActive?: boolean
  ): Subscriber['status'] => {
    if (typeof value === 'string') {
      const normalized = value.trim().toLowerCase();
      switch (normalized) {
        case 'active':
          return 'Active';
        case 'expired':
          return 'Expired';
        case 'cancelled':
        case 'canceled':
          return 'Cancelled';
        case 'pending':
          return 'Pending';
        case 'free trial':
        case 'freetrial':
        case 'free_trial':
          return 'Free Trial';
        default:
          break;
      }
    }
    if (typeof fallBackActive === 'boolean') {
      return fallBackActive ? 'Active' : 'Pending';
    }
    return 'Pending';
  };

  const formatPlanType = (value: unknown): string => {
    if (typeof value !== 'string') {
      return '--';
    }
    const normalized = value.trim().toLowerCase();
    if (!normalized) {
      return '--';
    }
    if (normalized === 'monthly') {
      return 'Monthly';
    }
    if (
      normalized === 'yearly' ||
      normalized === 'annual' ||
      normalized === 'annually'
    ) {
      return 'Yearly';
    }
    return `${normalized.charAt(0).toUpperCase()}${normalized.slice(1)}`;
  };

  const getStatusBadgeClasses = (status: Subscriber['status']): string => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Expired':
        return 'bg-orange-100 text-orange-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      case 'Pending':
        return 'bg-blue-100 text-blue-800';
      case 'Free Trial':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getStringValue = (...values: unknown[]): string | undefined => {
    for (const value of values) {
      if (typeof value === 'string' && value.trim() !== '') {
        return value;
      }
      if (typeof value === 'number' && !Number.isNaN(value)) {
        return String(value);
      }
    }
    return undefined;
  };

  const getNumberValue = (...values: unknown[]): number | undefined => {
    for (const value of values) {
      if (typeof value === 'number' && !Number.isNaN(value)) {
        return value;
      }
      if (typeof value === 'string' && value.trim() !== '') {
        const parsed = Number(value);
        if (!Number.isNaN(parsed)) {
          return parsed;
        }
      }
    }
    return undefined;
  };

  const getRecord = (value: unknown): Record<string, unknown> | undefined => {
    if (value && typeof value === 'object') {
      return value as Record<string, unknown>;
    }
    return undefined;
  };

  const mapSubscribers = useCallback(
    (items: unknown[]): Subscriber[] => {
      return items
        .map((item, index) => {
          if (!item || typeof item !== 'object') {
            return null;
          }

          const record = item as Record<string, unknown>;
          const plan = getRecord(record.plan);
          const admin = getRecord(record.admin);
          const user = getRecord(record.user);
          const organisation = getRecord(record.organisation);
          const organization = getRecord(record.organization);
          const subscription = getRecord(record.subscription);
          const subscriptionPlan = subscription
            ? getRecord(subscription.plan)
            : undefined;

          const planName =
            getStringValue(
              record.planName,
              plan?.planName,
              plan?.name,
              record.plan,
              subscription?.planName,
              subscriptionPlan?.planName,
              subscriptionPlan?.name
            ) ?? '';
          const planTypeRaw =
            getStringValue(
              record.billingType,
              record.billing_type,
              record.subscriptionType,
              record.subscription_type,
              record.planType,
              record.plan_type,
              plan?.billingType,
              plan?.billing_type,
              plan?.subscriptionType,
              plan?.subscription_type,
              plan?.planType,
              plan?.type,
              subscription?.billingType,
              subscription?.billing_type,
              subscription?.subscriptionType,
              subscription?.subscription_type,
              subscription?.validity
            ) ?? '';

          // Get subscriptionType to determine if we should use calculateTotalWithTax
          const subscriptionType = getStringValue(
            record.subscriptionType,
            record.subscription_type,
            subscription?.subscriptionType,
            subscription?.subscription_type,
            plan?.subscriptionType,
            plan?.subscription_type
          )?.toLowerCase();

          const totalAmount =
            getNumberValue(
              record.amount,
              record.totalAmount,
              plan?.amount,
              plan?.price,
              subscription?.totalAmount,
              subscription?.amount,
              subscriptionPlan?.amount,
              subscriptionPlan?.price
            ) ?? 0;

          const amount =
            subscriptionType === 'clinic' && totalAmount > 0
              ? calculateTotalWithTax(totalAmount)
              : totalAmount;
          const startDate =
            getStringValue(
              record.startDate,
              record.start_date,
              subscription?.startDate,
              subscription?.start_date
            ) ?? '';
          const expiryDate =
            getStringValue(
              record.validityExpiryDate,
              plan?.validityExpiryDate,
              record.endDate,
              record.end_date,
              record.expiryDate,
              record.expiry,
              record.validity,
              record.validity_date,
              subscription?.endDate,
              subscription?.end_date,
              subscription?.expiryDate
            ) ?? '';

          return {
            id:
              getStringValue(
                record.id,
                record._id,
                record.subscriberId,
                record.userId,
                subscription?.organizationId
              ) ?? String(index),
            no: record.no ?? (page - 1) * limit + index + 1,
            organisationName:
              getStringValue(
                record.organisationName,
                record.organizationName,
                record.name,
                organisation?.name,
                organization?.name
              ) ?? '--',
            adminEmail:
              getStringValue(
                record.adminEmail,
                record.email,
                record.contactEmail,
                admin?.email,
                user?.email
              ) ?? '--',
            planName: planName || '--',
            planType: formatPlanType(planTypeRaw),
            amount: formatCurrency(amount),
            validityStartDate: formatDateString(startDate),
            validityExpiryDate: formatDateString(expiryDate),
            status: formatSubscriberStatus(
              getStringValue(
                record.status,
                record.subscriptionStatus,
                record.subscription_status,
                record.subscription_state,
                record.currentStatus,
                record.state,
                subscription?.status
              ),
              typeof record.isActive === 'boolean'
                ? record.isActive
                : (
                    subscription?.status as string | undefined
                  )?.toLowerCase() === 'active'
            ),
            subscriptionType: subscriptionType || undefined,
            organizationType:
              subscriptionType &&
              subscriptionType.charAt(0).toUpperCase() +
                subscriptionType.slice(1),
            billingValidity:
              getStringValue(
                record.billingType,
                record.billing_type,
                subscription?.billingType,
                subscription?.validity
              ) || undefined,
          };
        })
        .filter((subscriber) => subscriber !== null) as Subscriber[];
    },
    [limit, page]
  );

  const mapRequestedSubscribers = useCallback(
    (items: unknown[]): RequestedSubscriber[] => {
      return items
        .map((item, index) => {
          if (!item || typeof item !== 'object') {
            return null;
          }

          const record = item as Record<string, unknown>;
          const subscription = getRecord(record.subscription);

          const name =
            getStringValue(
              record.name,
              record.subscriberName,
              record.userName
            ) ?? '';

          const email =
            getStringValue(
              record.email,
              record.subscriberEmail,
              record.contactEmail
            ) ?? '';

          const currentOrganization =
            getStringValue(
              record.organizationName,
              record.currentOrganization,
              subscription?.organizationName
            ) ?? '--';

          const requestedClinicName =
            getStringValue(
              record.requestedOrganizationName,
              record.requestedClinicName
            ) ?? '--';

          const planName =
            getStringValue(record.planName, subscription?.planName) ?? '--';

          const planStatus =
            getStringValue(
              subscription?.status,
              record.status,
              record.planStatus
            ) ?? '--';

          return {
            id:
              getStringValue(record.id, record._id, record.subscriberId) ??
              String(index),
            name,
            email,
            currentOrganization,
            requestedClinicName,
            planName,
            planStatus,
          };
        })
        .filter((subscriber) => subscriber !== null) as RequestedSubscriber[];
    },
    []
  );

  const fetchSubscribers = useCallback(
    async (options?: { force?: boolean }) => {
      if (activeTab !== 'subscribers' && !options?.force) return;

      const trimmedSearch = searchValue.trim();
      const normalizedStatus = selectedStatus
        ? selectedStatus.toLowerCase()
        : undefined;

      const currentPage = page;

      const queryKey = JSON.stringify({
        page: currentPage,
        limit,
        search: trimmedSearch || undefined,
        planId: selectedSubscription || undefined,
        status: normalizedStatus,
      });

      if (
        !options?.force &&
        hasFetchedSubscribersRef.current &&
        subscribersQueryKeyRef.current === queryKey
      ) {
        return;
      }

      if (subscribersQueryKeyRef.current !== queryKey) {
        setSubscribers([]);
      }

      setLoadingSubscribers(true);
      try {
        const filters: GetSubscribersParams = {
          page: currentPage,
          limit,
        };

        if (trimmedSearch) {
          filters.search = trimmedSearch;
        }

        if (selectedSubscription) {
          filters.planId = selectedSubscription;
        }

        if (normalizedStatus) {
          filters.status = normalizedStatus;
        }

        const response = await getSubscribers(filters);

        const mapped = mapSubscribers(response.items);
        setSubscribers(mapped);
        setTotalSubscribers(response.total ?? mapped.length);

        let nextLimit = limit;
        if (typeof response.limit === 'number' && response.limit > 0) {
          nextLimit = response.limit;
          if (response.limit !== limit) {
            setLimit(response.limit);
          }
        }

        let nextPage = page;
        if (typeof response.page === 'number' && response.page > 0) {
          nextPage = response.page;
          setPage((current) =>
            current === response.page ? current : response.page
          );
        }

        subscribersQueryKeyRef.current = JSON.stringify({
          page: nextPage,
          limit: nextLimit,
          search: trimmedSearch || undefined,
          planId: selectedSubscription || undefined,
          status: normalizedStatus,
        });
        hasFetchedSubscribersRef.current = true;
      } catch (err: unknown) {
        if (isCancelledRequestError(err)) {
          return;
        }
        const errorMessage =
          (err as { response?: { data?: { message?: string } } })?.response
            ?.data?.message ||
          (err as { message?: string })?.message ||
          'Failed to load subscribers';
        showError('Error', errorMessage);
      } finally {
        setLoadingSubscribers(false);
      }
    },
    [
      activeTab,
      limit,
      mapSubscribers,
      page,
      searchValue,
      selectedStatus,
      selectedSubscription,
      showError,
    ]
  );

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleRequestedPageChange = useCallback((newPage: number) => {
    setRequestedPage(newPage);
  }, []);

  const fetchRequestedSubscribers = useCallback(
    async (options?: { force?: boolean }) => {
      if (activeTab !== 'requested-subscribers' && !options?.force) return;

      const trimmedSearch = requestedSearchValue.trim();
      const currentPage = requestedPage;

      const queryKey = JSON.stringify({
        page: currentPage,
        limit,
        search: trimmedSearch || undefined,
        setupStatus: requestedStatus,
      });

      if (
        !options?.force &&
        requestedSubscribersQueryKeyRef.current === queryKey
      ) {
        return;
      }

      if (requestedSubscribersQueryKeyRef.current !== queryKey) {
        setRequestedSubscribers([]);
      }

      setLoadingRequestedSubscribers(true);
      try {
        const filters: GetSubscribersParams = {
          page: currentPage,
          limit,
          setupStatus: requestedStatus,
        };

        if (trimmedSearch) {
          filters.requestedOrganizationName = trimmedSearch;
        }

        const response = await getSubscribers(filters);

        const mapped = mapRequestedSubscribers(response.items);
        setRequestedSubscribers(mapped);
        setTotalRequestedSubscribers(response.total ?? mapped.length);

        let nextLimit = limit;
        if (typeof response.limit === 'number' && response.limit > 0) {
          nextLimit = response.limit;
          if (response.limit !== limit) {
            setLimit(response.limit);
          }
        }

        let nextPage = requestedPage;
        if (typeof response.page === 'number' && response.page > 0) {
          nextPage = response.page;
          setRequestedPage((current) =>
            current === response.page ? current : response.page
          );
        }

        requestedSubscribersQueryKeyRef.current = JSON.stringify({
          page: nextPage,
          limit: nextLimit,
          search: trimmedSearch || undefined,
          setupStatus: requestedStatus,
        });
      } catch (err: unknown) {
        if (isCancelledRequestError(err)) {
          return;
        }
        const errorMessage =
          (err as { response?: { data?: { message?: string } } })?.response
            ?.data?.message ||
          (err as { message?: string })?.message ||
          'Failed to load requested subscribers';
        showError('Error', errorMessage);
      } finally {
        setLoadingRequestedSubscribers(false);
      }
    },
    [
      activeTab,
      limit,
      mapRequestedSubscribers,
      requestedPage,
      requestedSearchValue,
      requestedStatus,
      showError,
    ]
  );

  useEffect(() => {
    if (activeTab !== 'subscribers') return;

    const handler = setTimeout(() => {
      fetchSubscribers();
    }, 300);

    return () => clearTimeout(handler);
  }, [activeTab, fetchSubscribers]);

  useEffect(() => {
    if (activeTab !== 'requested-subscribers') return;

    const handler = setTimeout(() => {
      fetchRequestedSubscribers();
    }, 300);

    return () => clearTimeout(handler);
  }, [activeTab, fetchRequestedSubscribers]);

  useEffect(() => {
    if (page !== 1) {
      setPage(1);
    }

    // Clear subscribers immediately when search/filters change to prevent showing old data
    setSubscribers([]);
    setLoadingSubscribers(true);
    hasFetchedSubscribersRef.current = false;
    subscribersQueryKeyRef.current = null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchValue, selectedStatus, selectedSubscription]);

  useEffect(() => {
    if (!isSubscriberModalOpen || !isEditMode || !editingSubscriberId) {
      return;
    }

    let isActive = true;

    const fetchSubscriberDetail = async () => {
      setIsLoadingSubscriber(true);
      try {
        const detail = await getSubscriberById(editingSubscriberId);
        if (!isActive) return;
        setEditingSubscriberData(detail);
      } catch (err: unknown) {
        if (!isActive || isCancelledRequestError(err)) {
          setIsLoadingSubscriber(false);
          return;
        }
        const errorMessage =
          (err as { response?: { data?: { message?: string } } })?.response
            ?.data?.message ??
          (err as { message?: string })?.message ??
          'Failed to load subscriber details';
        showError('Error', errorMessage);
      } finally {
        if (isActive) {
          setIsLoadingSubscriber(false);
        }
      }
    };

    fetchSubscriberDetail();

    return () => {
      isActive = false;
    };
  }, [editingSubscriberId, isEditMode, isSubscriberModalOpen, showError]);

  // Clear requested subscribers when search/filters change
  useEffect(() => {
    if (requestedPage !== 1) {
      setRequestedPage(1);
    }

    setRequestedSubscribers([]);
    setLoadingRequestedSubscribers(true);
    requestedSubscribersQueryKeyRef.current = null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestedSearchValue, requestedStatus]);

  // Handler functions for requested subscribers
  const handleRequestedSearchChange = useCallback((searchTerm: string) => {
    setRequestedSearchValue(searchTerm);
  }, []);

  const handleRequestedStatusChange = useCallback((status: string) => {
    setRequestedStatus(status as 'pending_setup' | 'setup_complete');
    setSelectedRequestedSubscribers([]); // Clear selection when status changes
  }, []);

  const handleSelectRequestedSubscriber = useCallback(
    (subscriberId: string, selected: boolean) => {
      if (selected) {
        setSelectedRequestedSubscribers((prev) => [...prev, subscriberId]);
      } else {
        setSelectedRequestedSubscribers((prev) =>
          prev.filter((id) => id !== subscriberId)
        );
      }
    },
    []
  );

  const handleSelectAllRequestedSubscribers = useCallback(() => {
    const allIds = requestedSubscribers.map((sub) => sub.id);
    setSelectedRequestedSubscribers(allIds);
  }, [requestedSubscribers]);

  const handleAddToOrganization = useCallback(() => {
    setIsAddModalOpen(true);
  }, []);

  const handleRemoveFromOrganization = useCallback(() => {
    setIsRemoveModalOpen(true);
  }, []);

  const handleAddModalClose = useCallback(() => {
    setIsAddModalOpen(false);
  }, []);

  const handleRemoveModalClose = useCallback(() => {
    setIsRemoveModalOpen(false);
  }, []);

  const handleAddToOrganizationConfirm = useCallback(
    async (organizationId: string, subscriptionIds: string[]) => {
      setIsMappingLoading(true);
      try {
        const response = await mapUserToClinicOrg({
          organizationId,
          subscriptionIds,
        });

        // Extract success messages from API response
        let successMessage = 'Subscribers added to organization successfully';
        if (response.results?.success && response.results.success.length > 0) {
          // Use the first success message, or create a summary
          const successCount =
            response.successCount || response.results.success.length;
          const firstMessage = response.results.success[0]?.message;
          if (firstMessage) {
            successMessage =
              successCount > 1
                ? `${successCount} subscribers added successfully. ${firstMessage}`
                : firstMessage;
          } else {
            successMessage = `${successCount} subscriber(s) added to organization successfully`;
          }
        } else if (response.message) {
          successMessage = response.message;
        }

        success('Success', successMessage);
        handleAddModalClose();
        setSelectedRequestedSubscribers([]);
        fetchRequestedSubscribers({ force: true });
      } catch (error) {
        // Extract error messages from API response
        let errorMessage = 'Failed to add subscribers to organization';
        const errorResponse = (error as any)?.response?.data;

        if (
          errorResponse?.results?.failed &&
          errorResponse.results.failed.length > 0
        ) {
          // Use the first error message from failed results
          const firstError = errorResponse.results.failed[0];
          if (firstError?.message) {
            errorMessage = firstError.message;
          } else if (firstError?.error) {
            errorMessage = firstError.error;
          }
        } else if (errorResponse?.message) {
          errorMessage = errorResponse.message;
        } else if (errorResponse?.error) {
          errorMessage = errorResponse.error;
        }

        showError('Error', errorMessage);
      } finally {
        setIsMappingLoading(false);
      }
    },
    [success, showError, handleAddModalClose, fetchRequestedSubscribers]
  );

  const handleRemoveFromOrganizationConfirm = useCallback(
    async (organizationId: string, subscriptionIds: string[]) => {
      setIsMappingLoading(true);
      try {
        const response = await unmapUserFromClinicOrg({
          organizationId,
          subscriptionIds,
        });

        // Extract success messages from API response
        let successMessage =
          'Subscribers removed from organization successfully';
        if (response.results?.success && response.results.success.length > 0) {
          // Use the first success message, or create a summary
          const successCount =
            response.successCount || response.results.success.length;
          const firstMessage = response.results.success[0]?.message;
          if (firstMessage) {
            successMessage =
              successCount > 1
                ? `${successCount} subscribers removed successfully. ${firstMessage}`
                : firstMessage;
          } else {
            successMessage = `${successCount} subscriber(s) removed from organization successfully`;
          }
        } else if (response.message) {
          successMessage = response.message;
        }

        success('Success', successMessage);
        handleRemoveModalClose();
        setSelectedRequestedSubscribers([]);
        fetchRequestedSubscribers({ force: true });
      } catch (error) {
        // Extract error messages from API response
        let errorMessage = 'Failed to remove subscribers from organization';
        const errorResponse = (error as any)?.response?.data;

        if (
          errorResponse?.results?.failed &&
          errorResponse.results.failed.length > 0
        ) {
          // Use the first error message from failed results
          const firstError = errorResponse.results.failed[0];
          if (firstError?.message) {
            errorMessage = firstError.message;
          } else if (firstError?.error) {
            errorMessage = firstError.error;
          }
        } else if (errorResponse?.message) {
          errorMessage = errorResponse.message;
        } else if (errorResponse?.error) {
          errorMessage = errorResponse.error;
        }

        showError('Error', errorMessage);
      } finally {
        setIsMappingLoading(false);
      }
    },
    [success, showError, handleRemoveModalClose, fetchRequestedSubscribers]
  );

  // Get selected subscriber details for modals
  const getSelectedRequestedSubscriberDetails = useCallback(() => {
    return requestedSubscribers
      .filter((sub) => selectedRequestedSubscribers.includes(sub.id))
      .map((sub) => ({
        id: sub.id,
        name: sub.name || 'Unknown',
        email: sub.email,
        requestedClinicName: sub.requestedClinicName,
        currentOrganization: sub.currentOrganization,
      }));
  }, [requestedSubscribers, selectedRequestedSubscribers]);

  return (
    <div className='space-y-6'>
      <style>{richTextStyles}</style>
      <div>
        <h1 className='text-2xl font-bold text-gray-900'>
          Payments & Subscriptions
        </h1>
        <p className='mt-1 text-sm text-gray-500'>
          Manage subscription plans and subscribers
        </p>
      </div>

      <div className='border-b border-gray-200'>
        <nav className='-mb-px flex space-x-8'>
          <button
            onClick={() => setActiveTab('subscriptions')}
            className={`
              whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              ${
                activeTab === 'subscriptions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Subscriptions
          </button>
          <button
            onClick={() => setActiveTab('subscribers')}
            className={`
              whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              ${
                activeTab === 'subscribers'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Subscribers
          </button>
          <button
            onClick={() => setActiveTab('requested-subscribers')}
            className={`
              whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              ${
                activeTab === 'requested-subscribers'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }
            `}
          >
            Clinical Way – Requested Subscribers
          </button>
        </nav>
      </div>

      {activeTab === 'subscribers' && (
        <div className='space-y-4'>
          <div className='flex flex-row gap-3 items-start justify-between'>
            <SearchBar
              value={searchValue}
              onChange={setSearchValue}
              placeholder='Search by name or email'
              size='md'
            />
            <div className='flex flex-row gap-3'>
              <MUISelect
                placeholder='All Plans'
                value={selectedSubscription}
                onChange={(e) => setSelectedSubscription(e.target.value)}
                options={planFilterOptions}
                sx={{ width: 256 }}
              />
              <MUISelect
                placeholder='All Status'
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                options={statusOptions}
                sx={{ width: 140 }}
              />
              <AppButton
                onClick={handleAddUser}
                startIcon={<Plus className='w-4 h-4' />}
                sx={{ minWidth: 140, width: 150 }}
              >
                Add New User
              </AppButton>
            </div>
          </div>

          <DataTable
            data={subscribers}
            columns={columns}
            loading={loadingSubscribers}
            pagination={{
              total: totalSubscribers,
              page,
              limit,
              onPageChange: handlePageChange,
            }}
            actions={subscriberActions}
          />
        </div>
      )}

      {activeTab === 'requested-subscribers' && (
        <div className='space-y-4'>
          <div className='flex flex-row gap-3 items-start justify-between'>
            <SearchBar
              value={requestedSearchValue}
              onChange={handleRequestedSearchChange}
              placeholder='Search requested clinic name'
              size='md'
            />
            <div className='flex flex-row gap-3'>
              <MUISelect
                value={requestedStatus}
                onChange={(e) => handleRequestedStatusChange(e.target.value)}
                options={requestedStatusOptions}
                sx={{ width: 200 }}
              />
              <AppButton
                onClick={
                  requestedStatus === 'pending_setup'
                    ? handleAddToOrganization
                    : handleRemoveFromOrganization
                }
                disabled={
                  selectedRequestedSubscribers.length === 0 || isMappingLoading
                }
                kind='primary'
                startIcon={
                  isMappingLoading ? (
                    <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                  ) : null
                }
                sx={{
                  backgroundColor:
                    requestedStatus === 'pending_setup' ? '#1976d2' : '#d32f2f',
                }}
              >
                {isMappingLoading
                  ? 'Processing...'
                  : requestedStatus === 'pending_setup'
                    ? `Add to Organization${selectedRequestedSubscribers.length > 0 ? ` (${selectedRequestedSubscribers.length})` : ''}`
                    : `Remove from Organization${selectedRequestedSubscribers.length > 0 ? ` (${selectedRequestedSubscribers.length})` : ''}`}
              </AppButton>
            </div>
          </div>

          <DataTable
            data={requestedSubscribers}
            columns={requestedColumns}
            loading={loadingRequestedSubscribers}
            selectedIds={selectedRequestedSubscribers}
            isAllSelected={false}
            onSelectAll={handleSelectAllRequestedSubscribers}
            onSelectOne={handleSelectRequestedSubscriber}
            pagination={{
              total: totalRequestedSubscribers,
              page: requestedPage,
              limit,
              onPageChange: handleRequestedPageChange,
            }}
            disableSelectAll={true}
          />
        </div>
      )}

      {activeTab === 'subscriptions' && (
        <div className='space-y-4'>
          <div className='flex justify-start items-center'>
            <AppButton
              onClick={() => setIsPlanModalOpen(true)}
              startIcon={<Plus className='w-4 h-4' />}
              sx={{ minWidth: 160 }}
            >
              Create New Plan
            </AppButton>
          </div>
          {/* Plans Grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            {/* Static Enterprise Plan */}
            <div className='bg-gray-200 rounded-lg shadow-sm p-6 relative w-full'>
              <button
                className='absolute top-3 right-3 inline-flex items-center justify-center h-8 w-8 rounded-md border border-gray-200 hover:bg-gray-50'
                title='Edit'
                onClick={() => {
                  if (!organizationPlan) {
                    fetchOrganizationPlan();
                  }
                  setIsEnterpriseModalOpen(true);
                }}
              >
                <Pencil className='w-4 h-4 text-gray-700' />
              </button>

              <div className='space-y-2'>
                <h3 className='text-lg font-semibold text-gray-900'>
                  {loadingOrganizationPlan && !organizationPlan
                    ? 'Loading...'
                    : organizationPlan?.planName || 'Organisation'}
                </h3>
                {loadingOrganizationPlan && !organizationPlan
                  ? renderRichText('', 'Fetching organisation plan', true)
                  : renderRichText(
                      organizationPlan?.description,
                      'Description',
                      true
                    )}
              </div>
            </div>

            {/* Fetched Plans */}
            {loadingPlans ? (
              <div className='col-span-full flex justify-center items-center py-8'>
                <p className='text-gray-500'>Loading plans...</p>
              </div>
            ) : (
              plans.map((plan) => {
                const featureNames = getAllFeatureNames(plan);
                const validityWithAmounts = formatValidityWithAmounts(plan);
                return (
                  <div
                    key={plan.id || plan.planName}
                    className='bg-white rounded-lg shadow p-6 relative w-full'
                  >
                    <div className='absolute top-3 right-3 flex gap-2'>
                      <button
                        className='inline-flex items-center justify-center h-8 w-8 rounded-md border border-gray-200 hover:bg-gray-50'
                        title='Edit'
                        onClick={() => plan.id && handleEditPlan(plan.id)}
                      >
                        <Pencil className='w-4 h-4 text-gray-700' />
                      </button>
                      {plan.id && (
                        <button
                          className='inline-flex items-center justify-center h-8 w-8 rounded-md border border-gray-200 hover:bg-red-50'
                          title='Delete'
                          onClick={() => handleDeleteClick(plan)}
                        >
                          <Trash2 className='w-4 h-4 text-red-600' />
                        </button>
                      )}
                    </div>

                    <div className='space-y-2'>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        {plan.planName}
                      </h3>

                      <p className='text-sm text-gray-600 font-medium'>
                        {validityWithAmounts}
                      </p>
                      {plan.created_on && (
                        <p className='text-[10px] text-gray-400'>
                          Created on: {formatDateString(plan.created_on)}
                        </p>
                      )}
                    </div>

                    {featureNames.length > 0 && (
                      <div className='mt-4 max-h-32 overflow-hidden'>
                        <div className='flex flex-wrap gap-3'>
                          {featureNames.map((featureName, index) => (
                            <span
                              key={index}
                              className='px-3 py-1 rounded-full bg-blue-50 text-blue-800 text-xs font-medium'
                            >
                              {featureName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}

      <Modal
        isOpen={isSubscriberModalOpen}
        onClose={handleCloseModal}
        title={isEditMode ? 'Edit Subscriber' : 'Add New Subscriber'}
        size='md'
      >
        <SubscriberForm
          onCancel={handleCloseModal}
          onSuccess={() => {
            handleCloseModal();
            // Refresh organizations list because subscriber can create/edit organizations
            dispatch(fetchAllOrganizations());
            fetchSubscribers({ force: true });
          }}
          isEditMode={isEditMode}
          {...(editingSubscriberId
            ? { subscriberId: editingSubscriberId }
            : {})}
          initialData={editingSubscriberData}
          isLoading={isLoadingSubscriber}
          plans={plans}
          plansLoading={loadingPlans}
        />
      </Modal>

      <PlanFormModal
        isOpen={isPlanModalOpen}
        onClose={handlePlanModalClose}
        onSuccess={handlePlanCreated}
      />

      <PlanEditModal
        isOpen={isEditPlanOpen}
        onClose={() => {
          setIsEditPlanOpen(false);
          setSelectedPlanId(null);
        }}
        planId={selectedPlanId}
        onPlanUpdated={handlePlanUpdated}
      />

      <EnterprisePlanModal
        isOpen={isEnterpriseModalOpen}
        onClose={() => {
          setIsEnterpriseModalOpen(false);
        }}
        plan={organizationPlan}
        onRefreshPlan={fetchOrganizationPlan}
        isRefreshingPlan={loadingOrganizationPlan}
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title='Delete Plan'
        message={`Are you sure you want to delete the plan "${planToDelete?.planName}"?`}
        confirmText='Delete'
        cancelText='Cancel'
        type='danger'
        loading={deletingPlanId !== null}
        {...(planToDelete?.planName && { itemName: planToDelete.planName })}
      />

      <AddToOrganizationModal
        isOpen={isAddModalOpen}
        onClose={handleAddModalClose}
        onConfirm={handleAddToOrganizationConfirm}
        selectedSubscribers={getSelectedRequestedSubscriberDetails()}
        loading={isMappingLoading}
      />

      <RemoveFromOrganizationModal
        isOpen={isRemoveModalOpen}
        onClose={handleRemoveModalClose}
        onConfirm={handleRemoveFromOrganizationConfirm}
        selectedSubscribers={getSelectedRequestedSubscriberDetails()}
        organizationId='' // This will be determined from the selected subscribers
        loading={isMappingLoading}
      />
    </div>
  );
};

export default PaymentList;
