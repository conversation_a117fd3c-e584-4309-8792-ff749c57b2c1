import { cognitoConfig } from '../../../utils/cognitoConfig';

// Helper to generate PKCE
function generateRandomString(length: number) {
  const charset =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';
  const values = new Uint32Array(length);
  window.crypto.getRandomValues(values);
  for (let i = 0; i < length; i++) {
    result += charset[(values[i] as number) % charset.length];
  }
  return result;
}

async function sha256(plain: string) {
  const encoder = new TextEncoder();
  const data = encoder.encode(plain);
  return window.crypto.subtle.digest('SHA-256', data);
}

function base64urlencode(a: ArrayBuffer) {
  let str = '';
  const bytes = new Uint8Array(a);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    str += String.fromCharCode(bytes[i] as number);
  }
  return btoa(str).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

export async function loginWithCognito() {
  // 1. CRITICAL: Clear ANY Cognito keys from localStorage before starting
  // This breaks the link between tabs.
  Object.keys(localStorage)
    .filter((key) => key.includes('Cognito') || key.includes('amplify'))
    .forEach((key) => localStorage.removeItem(key));

  // 2. PKCE Setup
  const verifier = generateRandomString(128);
  const challengeBuffer = await sha256(verifier);
  const challenge = base64urlencode(challengeBuffer);

  // Store verifier in sessionStorage for the callback
  sessionStorage.setItem('cognito_verifier', verifier);

  const params = new URLSearchParams({
    response_type: 'code',
    client_id: cognitoConfig.clientId,
    redirect_uri: cognitoConfig.redirectUri,
    scope: cognitoConfig.scopes.join(' '),
    prompt: 'login', // Forces the login form to appear, ensuring tab isolation
    code_challenge: challenge,
    code_challenge_method: 'S256',
  });

  const domain = cognitoConfig.cognitoDomain.replace(/^https?:\/\//, '');
  window.location.href = `https://${domain}/oauth2/authorize?${params.toString()}`;
}
