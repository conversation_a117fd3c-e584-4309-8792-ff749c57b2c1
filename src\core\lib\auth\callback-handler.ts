import { cognitoConfig } from '../../../utils/cognitoConfig';

function parseJwt(token: string) {
  try {
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (e) {
    return null;
  }
}

export async function handleCognitoCallback(searchParams: URLSearchParams) {
  const code = searchParams.get('code');
  if (!code) throw new Error('No code found');

  const verifier = sessionStorage.getItem('cognito_verifier');
  if (!verifier) throw new Error('No verifier found');

  const domain = cognitoConfig.cognitoDomain.replace(/^https?:\/\//, '');
  const body = new URLSearchParams({
    grant_type: 'authorization_code',
    client_id: cognitoConfig.clientId,
    code,
    redirect_uri: cognitoConfig.redirectUri,
    code_verifier: verifier,
  });

  const response = await fetch(`https://${domain}/oauth2/token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body,
  });

  if (!response.ok) {
    const err = await response.text();
    throw new Error(`Token exchange failed: ${err}`);
  }

  const tokens = await response.json();

  // Decode IdToken to get username (sub or cognito:username)
  const idTokenPayload = parseJwt(tokens.id_token);
  const username = idTokenPayload?.sub || idTokenPayload?.['cognito:username'];

  if (!username) throw new Error('Could not determine username from token');

  // Store in sessionStorage using Amplify v6 compatible keys
  // This allows fetchAuthSession() to work.
  const clientId = cognitoConfig.clientId;
  const prefix = `CognitoIdentityServiceProvider.${clientId}`;

  sessionStorage.setItem(`${prefix}.LastAuthUser`, username);
  sessionStorage.setItem(`${prefix}.${username}.idToken`, tokens.id_token);
  sessionStorage.setItem(
    `${prefix}.${username}.accessToken`,
    tokens.access_token
  );
  sessionStorage.setItem(
    `${prefix}.${username}.refreshToken`,
    tokens.refresh_token
  );
  sessionStorage.setItem(
    `${prefix}.${username}.clockDrift`,
    '0' // Or calculate it
  );

  // Clear PKCE verifier
  sessionStorage.removeItem('cognito_verifier');

  return tokens;
}
